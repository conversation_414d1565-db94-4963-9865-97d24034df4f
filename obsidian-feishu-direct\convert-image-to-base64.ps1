# PowerShell脚本：将图片转换为Base64
# 使用方法：将新的图片保存为 wechat-reward-new.jpg，然后运行此脚本

$imagePath = "assets\wechat-reward-new.jpg"
$outputPath = "assets\wechat-reward-base64-new.txt"

if (Test-Path $imagePath) {
    Write-Host "正在转换图片为Base64..."
    $base64 = [Convert]::ToBase64String([IO.File]::ReadAllBytes($imagePath))
    $base64 | Out-File -FilePath $outputPath -Encoding UTF8
    Write-Host "转换完成！Base64内容已保存到: $outputPath"
    Write-Host "请复制文件中的内容，替换 settings.ts 中 getRewardQRCodeBase64() 方法的返回值"
} else {
    Write-Host "错误：找不到图片文件 $imagePath"
    Write-Host "请将新的图片保存为 assets\wechat-reward-new.jpg"
}
